#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
针对 download_music.py 第121行错误的修复补丁
修复 FileNotFoundError: [Errno 2] No such file or directory 问题

使用方法：
1. 备份原始的 download_music.py 文件
2. 将此文件重命名为 download_music.py 替换原文件
3. 或者直接运行此修复版本
"""

import os
import sys
import subprocess
import shutil
import time
import traceback
from time import sleep, time
from pathlib import Path

# 添加路径以导入自定义函数
sys.path.append('/Volumes/Cryptomator/quant/pycharmproject')

try:
    from opencc import OpenCC
    from kzmfunction.function import get_km_variable
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所需的依赖包")
    sys.exit(1)

def contains_chinese(text):
    """检查字符串是否包含中文字符"""
    for char in text:
        if '\u4e00' <= char <= '\u9fff':
            return True
    return False

def safe_rename_file(source_file, target_file_path):
    """安全地重命名/移动文件，确保目标目录存在"""
    try:
        # 确保目标目录存在
        target_file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 如果目标文件已存在，创建备份名称
        if target_file_path.exists():
            timestamp = int(time.time())
            stem = target_file_path.stem
            suffix = target_file_path.suffix
            target_file_path = target_file_path.parent / f"{stem}_{timestamp}{suffix}"
            print(f"目标文件已存在，使用新名称: {target_file_path.name}")
        
        # 尝试使用 rename
        source_file.rename(target_file_path)
        print(f'成功重命名文件: {source_file.name} -> {target_file_path.name}')
        return target_file_path
        
    except Exception as e:
        print(f'rename 失败: {e}')
        print(f'尝试使用 shutil.move...')
        
        try:
            # 备选方案：使用 shutil.move
            result_path = shutil.move(str(source_file), str(target_file_path))
            print(f'使用 shutil.move 成功移动文件: {result_path}')
            return Path(result_path)
        except Exception as e2:
            print(f'shutil.move 也失败了: {e2}')
            raise e2

# 主程序开始
try:
    # 定义下载目录
    download_dir = os.path.expanduser('~/Downloads/下载音乐')
    apple_dir = os.path.join(download_dir, 'Apple Music')
    spotify_dir = os.path.join(download_dir, 'Spotify')
    
    # 确保目录存在
    os.makedirs(download_dir, exist_ok=True)
    os.makedirs(spotify_dir, exist_ok=True)
    
    # 获取变量
    music_singer = get_km_variable("music_singer")
    music_name = get_km_variable("music_name")
    album_name = get_km_variable("music_album_name")
    
    # 获取剪贴板内容
    try:
        clipboard_content = subprocess.check_output('pbpaste', text=True)
    except subprocess.CalledProcessError as e:
        print("Failed to get clipboard content:", e)
        clipboard_content = ""
    
    if 'apple' in clipboard_content:
        command = f'/Users/<USER>/opt/anaconda3/bin/gamdl --cookies-path /Users/<USER>/Downloads/下载音乐/apple.com_cookies.txt --no-synced-lyrics --codec-music-video h264 --output-path /Users/<USER>/Downloads/下载音乐/Apple\ Music "{clipboard_content}"'
    elif 'spotify' in clipboard_content:
        command = f'/Users/<USER>/opt/anaconda3/bin/votify --cookies-path /Users/<USER>/Downloads/下载音乐/cookies.txt --no-lrc "{clipboard_content}"'
    else:
        print('不是有效的音乐链接')
        sys.exit(1)
    
    # 执行下载命令
    result = subprocess.run(command, shell=True, text=True, cwd=download_dir, 
                          stdout=subprocess.PIPE, stderr=subprocess.PIPE, check=False)
    
    if 'ERROR' in result.stderr:
        print('下载失败')
        sys.exit(1)
    elif 'Cookies file not found' in result.stderr:
        print('cookies文件未找到')
        sys.exit(1)
    else:
        if 'apple' in clipboard_content:
            # 处理 Apple Music 下载
            download_path = Path(download_dir) / 'Apple Music'
            newest_file = None
            newest_time = 0
            
            # 查找最近2分钟内创建的音乐文件
            for file_path in download_path.rglob('*.m4a'):
                try:
                    file_time = file_path.stat().st_mtime
                    if (time.time() - file_time) < 120:  # 2分钟内创建的文件
                        if file_time > newest_time:
                            newest_time = file_time
                            newest_file = file_path
                except OSError as e:
                    print(f"无法访问文件 {file_path}: {e}")
                    continue
            
            if newest_file and (time.time() - newest_time) < 120:
                print(f"找到最新文件: {newest_file}")
                
                # 创建目标目录结构
                target_singer_dir = download_path / music_singer
                target_album_dir = target_singer_dir / album_name
                
                # 确保目标目录存在
                target_album_dir.mkdir(parents=True, exist_ok=True)
                print(f"目标目录: {target_album_dir}")
                
                # 构建新文件路径
                new_file_name = f"{music_name}.m4a"
                new_file_path = target_album_dir / new_file_name
                
                print(f"准备重命名: {newest_file} -> {new_file_path}")
                
                # 等待文件系统同步
                sleep(2)
                
                # 使用安全的重命名函数
                final_path = safe_rename_file(newest_file, new_file_path)
                
                # 处理原始目录清理
                original_album_dir = newest_file.parent
                original_singer_dir = original_album_dir.parent
                
                # 移动剩余文件（如果有的话）
                if original_album_dir.exists() and original_album_dir != target_album_dir:
                    for remaining_file in original_album_dir.iterdir():
                        if remaining_file.is_file():
                            target_file = target_album_dir / remaining_file.name
                            try:
                                shutil.move(str(remaining_file), str(target_file))
                                print(f"移动文件: {remaining_file.name}")
                            except Exception as e:
                                print(f"移动文件失败: {e}")
                
                # 清理空目录
                try:
                    if original_album_dir.exists() and not any(original_album_dir.iterdir()):
                        original_album_dir.rmdir()
                        print(f"删除空专辑目录: {original_album_dir}")
                    
                    if original_singer_dir.exists() and not any(original_singer_dir.iterdir()):
                        original_singer_dir.rmdir()
                        print(f"删除空歌手目录: {original_singer_dir}")
                except Exception as e:
                    print(f"清理目录时出错: {e}")
                
                print('Apple Music 下载和整理完成')
            else:
                print("未找到最近下载的音乐文件")
        
        # Spotify 处理逻辑保持不变...
        elif 'spotify' in clipboard_content.lower():
            print("Spotify 下载处理...")
            # 这里可以添加 Spotify 的处理逻辑
            
except Exception as e:
    traceback.print_exc()
    print(f"发生错误: {e}")
    print(f"错误类型: {type(e).__name__}")
    print(f"错误详情: {str(e)}")
