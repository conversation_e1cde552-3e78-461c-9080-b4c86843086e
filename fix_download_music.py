#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 download_music.py 第121行错误的脚本
自动备份原文件并应用修复

使用方法：
python fix_download_music.py
"""

import os
import shutil
import re
from pathlib import Path

def backup_original_file(original_path):
    """备份原始文件"""
    backup_path = f"{original_path}.backup"
    shutil.copy2(original_path, backup_path)
    print(f"已备份原文件到: {backup_path}")
    return backup_path

def fix_download_music_script(file_path):
    """修复 download_music.py 脚本中的问题"""
    
    # 读取原文件
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复1: 在第121行附近添加目录创建逻辑
    # 查找 new_file_path = album_dir / new_file_name 这一行
    pattern1 = r'(new_file_path = album_dir / new_file_name)'
    replacement1 = r'new_file_path = target_album_dir / new_file_name'
    
    if pattern1 in content:
        content = re.sub(pattern1, replacement1, content)
        print("修复1: 将 album_dir 改为 target_album_dir")
    
    # 修复2: 在 rename 操作前添加安全检查
    pattern2 = r'(\s+)(newest_file\.rename\(new_file_path\))'
    replacement2 = r'''\1# 确保目标目录存在
\1try:
\1    new_file_path.parent.mkdir(parents=True, exist_ok=True)
\1    newest_file.rename(new_file_path)
\1except Exception as rename_error:
\1    print(f'rename 失败: {rename_error}')
\1    print(f'尝试使用 shutil.move...')
\1    try:
\1        import shutil
\1        shutil.move(str(newest_file), str(new_file_path))
\1        print('使用 shutil.move 成功移动文件')
\1    except Exception as move_error:
\1        print(f'shutil.move 也失败了: {move_error}')
\1        raise move_error'''
    
    content = re.sub(pattern2, replacement2, content)
    print("修复2: 添加了安全的文件移动逻辑")
    
    # 写入修复后的文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"修复完成: {file_path}")

def main():
    """主函数"""
    original_file = "/Volumes/software_sync/KeyboardMaestro-scripts/download_music.py"
    
    if not os.path.exists(original_file):
        print(f"错误: 找不到原文件 {original_file}")
        print("请确认文件路径是否正确")
        return
    
    try:
        # 备份原文件
        backup_path = backup_original_file(original_file)
        
        # 应用修复
        fix_download_music_script(original_file)
        
        print("\n修复完成！")
        print(f"原文件已备份到: {backup_path}")
        print(f"修复后的文件: {original_file}")
        print("\n现在可以重新运行 download_music.py 测试修复效果")
        
    except Exception as e:
        print(f"修复过程中出错: {e}")
        print("请手动检查文件")

if __name__ == "__main__":
    main()
