#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音乐下载文件重命名脚本 - 修复版本
修复了目标目录不存在导致的 FileNotFoundError 问题
"""

import os
import re
import shutil
from pathlib import Path
import time

def clean_filename(filename):
    """清理文件名，移除不合法字符"""
    # 移除或替换不合法字符
    filename = re.sub(r'[<>:"/\\|?*]', '', filename)
    # 移除前导数字和空格
    filename = re.sub(r'^\d+\s*', '', filename)
    return filename.strip()

def translate_artist_name(artist_name):
    """翻译艺术家名字（示例映射）"""
    translation_map = {
        'Aaron Kwok': '郭富城',
        # 可以添加更多映射
    }
    return translation_map.get(artist_name, artist_name)

def translate_album_name(album_name):
    """翻译专辑名字（示例映射）"""
    translation_map = {
        'Absolute': '绝对',
        # 可以添加更多映射
    }
    return translation_map.get(album_name, album_name)

def translate_song_name(song_name):
    """翻译歌曲名字（示例映射）"""
    translation_map = {
        '被愛': '被爱',
        # 可以添加更多映射
    }
    return translation_map.get(song_name, song_name)

def main():
    try:
        # 下载目录
        download_dir = Path("/Users/<USER>/Downloads/下载音乐/Apple Music")
        
        if not download_dir.exists():
            print(f"下载目录不存在: {download_dir}")
            return
        
        # 查找最新的音乐文件
        music_files = []
        for ext in ['*.m4a', '*.mp3', '*.flac', '*.wav']:
            music_files.extend(download_dir.rglob(ext))
        
        if not music_files:
            print("未找到音乐文件")
            return
        
        # 按修改时间排序，获取最新文件
        newest_file = max(music_files, key=lambda f: f.stat().st_mtime)
        print(f"找到最新文件: {newest_file}")
        
        # 解析文件路径
        file_parts = newest_file.parts
        
        # 假设路径结构为: .../艺术家/专辑/歌曲文件
        if len(file_parts) < 3:
            print("文件路径结构不符合预期")
            return
        
        original_artist = file_parts[-3]  # 艺术家文件夹名
        original_album = file_parts[-2]   # 专辑文件夹名
        original_filename = file_parts[-1] # 文件名
        
        print(f"原始艺术家: {original_artist}")
        print(f"原始专辑: {original_album}")
        print(f"原始文件名: {original_filename}")
        
        # 翻译名称
        translated_artist = translate_artist_name(original_artist)
        translated_album = translate_album_name(original_album)
        
        # 清理并翻译歌曲名
        song_name = clean_filename(original_filename)
        song_name_without_ext = Path(song_name).stem
        translated_song = translate_song_name(song_name_without_ext)
        
        # 构建新的文件路径
        new_artist_dir = download_dir / translated_artist
        new_album_dir = new_artist_dir / translated_album
        new_filename = f"{translated_song}{newest_file.suffix}"
        new_file_path = new_album_dir / new_filename
        
        print(f"目标艺术家目录: {new_artist_dir}")
        print(f"目标专辑目录: {new_album_dir}")
        print(f"目标文件路径: {new_file_path}")
        
        # 确保目标目录存在
        try:
            new_album_dir.mkdir(parents=True, exist_ok=True)
            print(f"已创建目录: {new_album_dir}")
        except Exception as e:
            print(f"创建目录失败: {e}")
            return
        
        # 检查目标文件是否已存在
        if new_file_path.exists():
            print(f"目标文件已存在: {new_file_path}")
            # 可以选择覆盖或重命名
            timestamp = int(time.time())
            new_filename = f"{translated_song}_{timestamp}{newest_file.suffix}"
            new_file_path = new_album_dir / new_filename
            print(f"使用新文件名: {new_file_path}")
        
        # 移动文件
        try:
            # 使用 shutil.move 而不是 pathlib.rename，更加健壮
            shutil.move(str(newest_file), str(new_file_path))
            print(f"文件移动成功: {newest_file} -> {new_file_path}")
            
            # 检查原始目录是否为空，如果为空则删除
            original_album_dir = newest_file.parent
            original_artist_dir = original_album_dir.parent
            
            try:
                if original_album_dir.exists() and not any(original_album_dir.iterdir()):
                    original_album_dir.rmdir()
                    print(f"已删除空专辑目录: {original_album_dir}")
                
                if original_artist_dir.exists() and not any(original_artist_dir.iterdir()):
                    original_artist_dir.rmdir()
                    print(f"已删除空艺术家目录: {original_artist_dir}")
            except Exception as e:
                print(f"删除空目录时出错: {e}")
                
        except Exception as e:
            print(f"移动文件失败: {e}")
            return
            
    except Exception as e:
        print(f"发生错误: {e}")
        print(f"错误类型: {type(e).__name__}")
        print(f"错误详情: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
