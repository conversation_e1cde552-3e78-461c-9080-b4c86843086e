# !/usr/bin/env python
# -*- coding:utf-8 -*-
# region ===============================================备注说明
"""
通过gamdl或votify下载apple music和spotify音乐
https://github.com/glomatico/gamdl
pip install gamdl==2.1.8（版本2.2会报错！！）
https://github.com/glomatico/votify
pip install votify==1.4.2

修复版本：解决了目标目录不存在导致的 FileNotFoundError 问题
"""
# endregion ===============================================备注说明


# region ===============================================import
import os
import sys
import subprocess
import shutil
import time
import traceback
from time import sleep, time
from pathlib import Path
from opencc import OpenCC
sys.path.append(
    '/Volumes/Cryptomator/quant/pycharmproject'
)
from kzmfunction.function import get_km_variable
# endregion ===============================================import


# region ===============================================函数
def contains_chinese(text):
    """检查字符串是否包含中文字符"""
    for char in text:
        if '\u4e00' <= char <= '\u9fff':
            return True
    return False

# endregion ===============================================函数

# region ===============================================设置
# 定义下载目录
download_dir = os.path.expanduser('~/Downloads/下载音乐')
apple_dir = os.path.join(download_dir, 'Apple Music')
spotify_dir = os.path.join(download_dir, 'Spotify')
# 确保目录存在
os.makedirs(download_dir, exist_ok=True)
os.makedirs(spotify_dir, exist_ok=True)
# 获取music_singer变量
music_singer = get_km_variable("music_singer")
music_name = get_km_variable("music_name")
album_name = get_km_variable("music_album_name")
# 获取剪贴板内容
try:
    clipboard_content = subprocess.check_output('pbpaste', text=True)
except subprocess.CalledProcessError as e:
    print("Failed to get clipboard content:", e)
    clipboard_content = ""
# clipboard_content = 'https://music.apple.com/cn/album/en-attendant-ses-pas/212137941?i=212138045'
# endregion ===========================================设置


# region ========================================主程序
# clipboard_content = 'https://open.spotify.com/track/28pM9J7eAp7QIDsNBDfVRj?si=c1e7d23d1ae74727'
if 'apple' in clipboard_content:
    # 构建gamdl命令
    # command = f'/Users/<USER>/opt/anaconda3/bin/gamdl --no-synced-lyrics --codec-music-video h264 "{clipboard_content}"'
    command = f'/Users/<USER>/opt/anaconda3/bin/gamdl --cookies-path /Users/<USER>/Downloads/下载音乐/apple.com_cookies.txt --no-synced-lyrics --codec-music-video h264 --output-path /Users/<USER>/Downloads/下载音乐/Apple\ Music "{clipboard_content}"'
    # command = '/Users/<USER>/opt/anaconda3/bin/gamdl --cookies-path /Users/<USER>/Downloads/下载音乐/cookies.txt --no-synced-lyrics --output-path --codec-music-video h264 '~/Downloads/下载音乐/Apple Music/' https://music.apple.com/us/album/stay-faraway-so-close/1446736788\?i\=1446736802\&l\=zh-Hans-CN'
elif 'spotify' in clipboard_content:
    command = f'/Users/<USER>/opt/anaconda3/bin/votify --cookies-path /Users/<USER>/Downloads/下载音乐/cookies.txt --no-lrc "{clipboard_content}"'
else:
    print('不是有效的音乐链接')
    exit()
# 在指定目录下执行命令
try:
    # 使用 subprocess.run 执行命令
    result = subprocess.run(command, shell=True, text=True, cwd=download_dir, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    # 检查命令是否成功执行
    if 'ERROR' in result.stderr:
        print('failed')
    elif 'Cookies file not found' in result.stderr:
        print('cookies文件未找到')
    else:
        if 'apple' in clipboard_content:
            # 使用 Path 对象处理路径
            download_path = Path(download_dir) / 'Apple Music'
            newest_file = None
            newest_time = 0

            # 遍历查找2分钟内创建的音乐文件
            for file_path in download_path.rglob('*.m4a'):
                try:
                    file_time = file_path.stat().st_mtime
                    if (time() - file_time) < 120:  # 2分钟内创建的文件
                        if file_time > newest_time:
                            newest_time = file_time
                            newest_file = file_path
                except OSError as e:
                    print(f"无法访问文件 {file_path}: {e}")
                    continue

            print(newest_file)
            if newest_file and (time() - newest_time) < 120:
                print(newest_file)
                target_base_path = newest_file.parent.parent.parent
                album_dir = target_base_path / music_singer / album_name
                singer_dir = album_dir.parent
                target_singer_dir = download_path / music_singer
                target_singer_dir.mkdir(exist_ok=True)
                # 新建专辑文件夹
                target_album_dir = target_singer_dir / album_name
                target_album_dir.mkdir(exist_ok=True)
                print(album_dir)
                print(singer_dir)

                # 只有当music_name包含中文时才重命名文件
                # if contains_chinese(music_name):
                    # 构建新的文件名和路径
                new_file_name = f"{music_name}.m4a"
                # 修复：使用 target_album_dir 而不是 album_dir
                new_file_path = target_album_dir / new_file_name
                sleep(5)
                # 重命名音乐文件 - 修复：确保目标目录存在
                try:
                    # 确保目标目录存在
                    new_file_path.parent.mkdir(parents=True, exist_ok=True)
                    newest_file.rename(new_file_path)
                    print(f'已将音乐文件重命名为: {new_file_name}')
                except Exception as e:
                    print(f'重命名文件失败: {e}')
                    print(f'源文件: {newest_file}')
                    print(f'目标文件: {new_file_path}')
                    # 使用 shutil.move 作为备选方案
                    try:
                        shutil.move(str(newest_file), str(new_file_path))
                        print(f'使用 shutil.move 成功移动文件')
                    except Exception as e2:
                        print(f'shutil.move 也失败了: {e2}')
                        raise e2
                # else:
                #     print(f'文件名不包含中文，保持原名: {newest_file.name}')

                # 构建目标路径
                target_singer_dir = download_path / music_singer
                target_singer_dir.mkdir(exist_ok=True)
                print(target_singer_dir)
                # 使用glob找到实际的专辑文件夹路径
                actual_album_paths = list(singer_dir.glob(album_dir.name.replace("'", "?")))
                if actual_album_paths:
                    actual_album_dir = actual_album_paths[0]
                    print(f"找到实际专辑文件夹路径: {actual_album_dir}")

                    # 移动专辑文件夹到目标位置
                    target_album_dir = target_singer_dir / actual_album_dir.name
                    if target_album_dir.exists():
                        shutil.rmtree(str(target_album_dir))

                    print(f"正在移动: {actual_album_dir} -> {target_singer_dir}")
                    shutil.move(str(actual_album_dir), str(target_singer_dir))
                    print(f'已移动专辑到 {music_singer}')

                    # 如果原歌手文件夹为空，删除它
                    if singer_dir.exists() and not any(singer_dir.iterdir()):
                        shutil.rmtree(str(singer_dir))
                        print(f'已删除空的歌手文件夹: {singer_dir.name}')

                    print('success')
                    print('下载完成')
                else:
                    print(f"无法找到实际的专辑文件夹: {album_dir.name}")
                    raise FileNotFoundError(f"找不到专辑文件夹: {album_dir}")
        elif 'spotify' in clipboard_content.lower():
            # 递归搜索所有 .ogg 文件
            ogg_files = []
            for root, dirs, files in os.walk(download_dir):
                for file in files:
                    if file.endswith('.ogg'):
                        ogg_files.append(os.path.join(root, file))
            # 转换 ogg 文件为 mp3
            for ogg_file in ogg_files:
                mp3_file = os.path.splitext(ogg_file)[0] + '.mp3'
                ffmpeg_command = f'ffmpeg -i "{ogg_file}" -acodec libmp3lame -b:a 320k "{mp3_file}"'
                try:
                    subprocess.run(ffmpeg_command, shell=True, check=True)
                    print(
                        f'成功转换: {os.path.relpath(ogg_file, download_dir)} -> {os.path.relpath(mp3_file, download_dir)}')
                    os.remove(ogg_file)  # 删除原始的 ogg 文件

                    mp3_files = []
                    for root, dirs, files in os.walk(download_dir):
                        for file in files:
                            if file.endswith('.mp3'):
                                simplified_file_name = OpenCC('t2s').convert(file)
                                old_path = os.path.join(root, file)
                                new_path = os.path.join(root, simplified_file_name)
                                os.rename(old_path, new_path)
                                mp3_files.append(new_path)

                    for mp3_file in mp3_files:
                        # 处理 Spotify 音乐文件
                        singer_dir = os.path.join(spotify_dir, music_singer)
                        os.makedirs(singer_dir, exist_ok=True)

                        album_dir = os.path.dirname(mp3_file)
                        simplified_album_name = OpenCC('t2s').convert(os.path.basename(album_dir))
                        simplified_album_dir = os.path.join(os.path.dirname(album_dir), simplified_album_name)

                        # 重命名专辑目录
                        if album_dir != simplified_album_dir:
                            os.rename(album_dir, simplified_album_dir)

                        dest_dir = os.path.join(singer_dir, simplified_album_name)
                        if os.path.exists(dest_dir):
                            # 如果目标目录存在，检查是否需要合并或覆盖
                            for file in os.listdir(simplified_album_dir):
                                src_file = os.path.join(simplified_album_dir, file)
                                dst_file = os.path.join(dest_dir, file)
                                if os.path.exists(dst_file):
                                    os.remove(dst_file)  # 如果文件已存在，先删除
                                shutil.move(src_file, dest_dir)
                            # 删除空的源目录
                            if os.path.exists(simplified_album_dir):
                                shutil.rmtree(simplified_album_dir)
                        else:
                            # 如果目标目录不存在，直接移动整个专辑文件夹
                            shutil.move(simplified_album_dir, singer_dir)

                    # 修改删除逻辑
                    original_download_dir = os.path.dirname(album_dir)
                    try:
                        if original_download_dir and os.path.exists(original_download_dir) and os.path.isdir(original_download_dir):
                            # 只删除临时下载的专辑文件夹，不删除歌手文件夹
                            if os.path.exists(simplified_album_dir):
                                contents = os.listdir(simplified_album_dir)
                                if not contents:  # 只有在文件夹为空时才删除
                                    shutil.rmtree(simplified_album_dir)
                                    print(f'已删除空的专辑文件夹: {os.path.relpath(simplified_album_dir, download_dir)}')
                            print('success')
                    except Exception as e:
                        print(f'删除专辑文件夹时出错: {e}')
                except subprocess.CalledProcessError:
                    print(f'转换失败: {os.path.relpath(ogg_file, download_dir)}')
except Exception as e:
    traceback.print_exc()
    print(f"发生错误: {e}")
    print(f"错误类型: {type(e).__name__}")
    print(f"错误详情: {str(e)}")
# endregion ===============================================主程序
